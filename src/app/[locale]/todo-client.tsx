"use client";
import { trpc } from "@/trpc/client";
import { useState } from "react";
import { X } from "lucide-react";
import { useTranslations } from "next-intl";

export const TodoClient = () => {
  const t = useTranslations("Todo");
  const [newTodo, setNewTodo] = useState("");
  const utils = trpc.useUtils();

  const { data: todos } = trpc.user.getTodos.useQuery();

  const addTodoMutation = trpc.user.addTodo.useMutation({
    onSuccess: () => {
      setNewTodo("");
      utils.user.getTodos.invalidate();
    },
  });

  const toggleTodoMutation = trpc.user.toggleTodo.useMutation({
    onSuccess: () => {
      utils.user.getTodos.invalidate();
    },
  });

  const deleteTodoMutation = trpc.user.deleteTodo.useMutation({
    onSuccess: () => {
      utils.user.getTodos.invalidate();
    },
  });

  const handleAddTodo = (e: React.FormEvent) => {
    e.preventDefault();
    if (newTodo.trim()) {
      addTodoMutation.mutate({ title: newTodo });
    }
  };

  return (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
      <h1 className="text-2xl font-bold text-navy-800 mb-4">{t("title")} 📝</h1>

      <form onSubmit={handleAddTodo} className="flex mb-6">
        <input
          type="text"
          placeholder="add your task here"
          value={newTodo}
          onChange={(e) => setNewTodo(e.target.value)}
          className="flex-grow py-2 px-4 bg-gray-100 rounded-l-full text-gray-700 focus:outline-none"
        />
        <button
          type="submit"
          className="bg-coral-500 hover:bg-coral-600 text-white py-2 px-8 rounded-r-full font-medium"
        >
          Add
        </button>
      </form>

      <div className="space-y-3">
        {todos?.map((todo) => (
          <div
            key={todo.id}
            className="flex items-center justify-between group"
          >
            <div className="flex items-center">
              <button
                onClick={() =>
                  toggleTodoMutation.mutate({
                    id: todo.id,
                    completed: !todo.completed,
                  })
                }
                className={`w-6 h-6 rounded-full border mr-3 flex items-center justify-center ${
                  todo.completed
                    ? "bg-coral-500 border-coral-500"
                    : "border-gray-300"
                }`}
              >
                {todo.completed && (
                  <svg
                    className="w-4 h-4 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                )}
              </button>
              <span
                className={`${
                  todo.completed
                    ? "line-through text-gray-500"
                    : "text-gray-900"
                }`}
              >
                {todo.title}
              </span>
            </div>
            <button
              onClick={() => deleteTodoMutation.mutate({ id: todo.id })}
              className="text-gray-400 hover:text-gray-600 ml-2"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};
