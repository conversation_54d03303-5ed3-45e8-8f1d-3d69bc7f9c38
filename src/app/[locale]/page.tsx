import { TodoClient } from "@/app/[locale]/todo-client";
import { auth } from "@/lib/auth";
import { getTranslations } from "next-intl/server";
import { headers } from "next/headers";

export default async function Home() {
  const t = await getTranslations("HomePage");
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session) {
    return <div>{t("notLoggedIn")}</div>;
  }

  return <TodoClient />;
}
