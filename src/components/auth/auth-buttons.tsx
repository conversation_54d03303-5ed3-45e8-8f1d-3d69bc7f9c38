"use client";

import { useState } from "react";
import { useSession } from "@/lib/auth-client";
import { UserButton } from "./user-button";
import { SignInForm } from "./sign-in-form";
import { SignUpForm } from "./sign-up-form";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";

export function AuthButtons() {
  const { data: session, isPending } = useSession();
  const [showSignIn, setShowSignIn] = useState(false);
  const [showSignUp, setShowSignUp] = useState(false);

  if (isPending) {
    return <div className="text-sm text-muted-foreground">Loading...</div>;
  }

  if (session?.user) {
    return <UserButton />;
  }

  return (
    <div className="flex gap-2">
      <Dialog open={showSignIn} onOpenChange={setShowSignIn}>
        <DialogTrigger asChild>
          <Button variant="outline">Sign In</Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[425px]">
          <SignInForm onSuccess={() => setShowSignIn(false)} />
        </DialogContent>
      </Dialog>

      <Dialog open={showSignUp} onOpenChange={setShowSignUp}>
        <DialogTrigger asChild>
          <Button>Sign Up</Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[425px]">
          <SignUpForm onSuccess={() => setShowSignUp(false)} />
        </DialogContent>
      </Dialog>
    </div>
  );
}
