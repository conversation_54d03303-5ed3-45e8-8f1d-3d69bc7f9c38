// Simple test script to verify better-auth is working
const fetch = require('node-fetch');

async function testAuth() {
  const baseURL = 'http://localhost:3000';
  
  console.log('Testing better-auth endpoints...');
  
  try {
    // Test sign-up
    console.log('\n1. Testing sign-up...');
    const signUpResponse = await fetch(`${baseURL}/api/auth/sign-up/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User'
      })
    });
    
    console.log('Sign-up status:', signUpResponse.status);
    const signUpData = await signUpResponse.text();
    console.log('Sign-up response:', signUpData);
    
    // Test sign-in
    console.log('\n2. Testing sign-in...');
    const signInResponse = await fetch(`${baseURL}/api/auth/sign-in/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    console.log('Sign-in status:', signInResponse.status);
    const signInData = await signInResponse.text();
    console.log('Sign-in response:', signInData);
    
  } catch (error) {
    console.error('Error testing auth:', error);
  }
}

testAuth();
